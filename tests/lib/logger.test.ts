/**
 * Tests for logger utility
 */

import { logger } from '@/lib/logger'

describe('Logger', () => {
  let consoleSpy: jest.SpyInstance

  beforeEach(() => {
    consoleSpy = jest.spyOn(console, 'info').mockImplementation()
  })

  afterEach(() => {
    consoleSpy.mockRestore()
  })

  it('logs info messages with correct format', () => {
    logger.info('ui', 'Test message', { test: 'data' })

    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('ℹ️ 🎨 [UI] Test message'),
      { test: 'data' }
    )
  })

  it('logs API requests', () => {
    logger.apiRequest('GET', '/api/test', { param: 'value' })

    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('🌐 [API] GET /api/test'),
      { requestData: { param: 'value' } }
    )
  })

  it('logs user actions', () => {
    logger.userAction('button_click', 'user123', { button: 'submit' })

    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('👤 [USER] User action: button_click'),
      { button: 'submit' }
    )
  })

  it('creates timer function that logs performance', () => {
    const endTimer = logger.startTimer('test_operation')
    
    // Mock performance.now to return predictable values
    const mockNow = jest.spyOn(performance, 'now')
    mockNow.mockReturnValueOnce(0).mockReturnValueOnce(100)
    
    endTimer()

    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('⚡ [PERFORMANCE] test_operation took 100ms'),
      undefined
    )

    mockNow.mockRestore()
  })
})
