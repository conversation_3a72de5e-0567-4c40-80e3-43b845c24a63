# Case Studies Application

A modern web application built with Next.js 15, React 19, and TypeScript, featuring a collection of case studies and press coverage showcasing how leading brands and organizations achieve remarkable growth through innovative strategies and data-driven campaigns. It features a beautiful UI powered by Tailwind CSS and Shadcn components.

## Tech Stack

- **Framework:** [Next.js 15](https://nextjs.org)
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **UI Components:** 
  - Shadcn UI components
  - Radix UI primitives
  - Framer Motion for animations
- **Database:** Supabase
- **Charts:** Recharts
- **State Management:** React (built-in hooks)
- **Package Manager:** Bun/npm

## Key Features

- Modern UI with responsive design
- Data visualization using Recharts
- Real-time updates with Supabase
- Twitter integration with react-tweet
- Toast notifications using Sonner
- Fully typed with TypeScript

## Project Structure

```
src/
├── app/          # Next.js app router pages and layouts
├── components/   # Reusable UI components
├── lib/          # Utility functions and configurations
└── middleware.ts # Next.js middleware for request handling
```

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   bun install
   # or
   npm install
   ```

3. Set up environment variables:
   Create a `.env.local` file with the necessary environment variables.

4. Run the development server:
   ```bash
   bun dev
   # or
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Development Notes

- To install new Shadcn components:
  ```bash
  bunx shadcn@latest add [component name]
  bun install
  ```

- When using the OpenAI ChatGPT API, use the model: "gpt-4o-mini"

## Available Scripts

- `dev`: Run development server
- `build`: Build for production
- `start`: Start production server
- `lint`: Run ESLint

## Dependencies

### Main Dependencies
- Next.js 15.1.6
- React & React DOM 19.0.0
- Supabase for database and authentication
- Radix UI components for accessible UI primitives
- Framer Motion for animations
- Recharts for data visualization
- Tailwind CSS for styling

### Dev Dependencies
- TypeScript 5
- ESLint 9
- PostCSS 8
- Various type definitions for TypeScript support

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.io/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Shadcn UI Documentation](https://ui.shadcn.com)

## Deployment

The application can be deployed on [Vercel](https://vercel.com) for optimal performance and integration with Next.js.
