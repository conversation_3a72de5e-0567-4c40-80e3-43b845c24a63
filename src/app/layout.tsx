import './globals.css'
import { Inter } from 'next/font/google'
import { cn } from '@/lib/utils'
import { Metadata } from 'next'
import { Toaster } from 'sonner'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'Case Studies & Press Coverage',
    template: '%s | Case Studies'
  },
  description: 'Discover how leading brands and organizations achieve remarkable growth through innovative strategies and data-driven campaigns.',
  keywords: ['case studies', 'marketing', 'social media', 'press coverage', 'growth strategies'],
  authors: [{ name: 'Case Studies Team' }],
  creator: 'Case Studies Team',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://case-studies.com',
    title: 'Case Studies & Press Coverage',
    description: 'Discover how leading brands and organizations achieve remarkable growth through innovative strategies and data-driven campaigns.',
    siteName: 'Case Studies',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Case Studies & Press Coverage',
    description: 'Discover how leading brands and organizations achieve remarkable growth through innovative strategies and data-driven campaigns.',
    creator: '@casestudies',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={cn(inter.className, "antialiased")} suppressHydrationWarning>
      <body className="min-h-screen bg-background font-sans antialiased" suppressHydrationWarning>
        {children}
        <Toaster position="top-right" richColors />
      </body>
    </html>
  )
}
