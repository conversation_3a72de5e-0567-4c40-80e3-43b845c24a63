export default function DashboardPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-24">
      <h1 className="text-4xl font-bold mb-8">Welcome to the Dashboard</h1>
      <p className="text-lg text-gray-600 mb-8">Select a section to get started</p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <a
          href="/case-studies"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Case Studies
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              →
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            View and manage case studies from your media team.
          </p>
        </a>
      </div>
    </div>
  )
}