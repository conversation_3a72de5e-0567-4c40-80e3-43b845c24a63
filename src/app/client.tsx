'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Eye, Heart, Repeat2, Link2, Twitter, LayoutGrid, List, Filter, Pencil } from 'lucide-react'
import { TweetWrapper } from '@/components/tweet-wrapper'
import { Input } from '@/components/ui/input'
import { 
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu'
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select"
import { createBrowserClient } from '@supabase/ssr'
import { CaseStudyDialog } from '@/components/case-study-dialog'
import { toast } from "sonner"
interface CaseStudy {
  id: bigint
  twitter_account: string
  twitter_link: string
  description: string
  categories: string[]
  date_of_post: Date
  impressions: bigint
  likes: bigint
  retweets: bigint
}

interface Department {
  id: string
  name: string
  description: string
  case_studies_admin_level: 'admin' | 'user'
}

type UserRole = 'admin' | 'editor' | 'guest'

interface CaseStudiesClientProps {
  department: Department
  allCaseStudies: CaseStudy[]
  allLabels: string[]
}

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    cookies: {
      get(name: string) {
        return document.cookie
          .split('; ')
          .find((row) => row.startsWith(`${name}=`))
          ?.split('=')[1] ?? ''
      },
      set(name: string, value: string, options: { path?: string; domain?: string; sameSite?: string; secure?: boolean; maxAge?: number }) {
        let cookieString = `${name}=${value}`
        if (options.maxAge) cookieString += `; max-age=${options.maxAge}`
        if (options.path) cookieString += `; path=${options.path}`
        if (options.domain) cookieString += `; domain=${options.domain}`
        if (options.sameSite) cookieString += `; samesite=${options.sameSite}`
        if (options.secure) cookieString += '; secure'
        document.cookie = cookieString
      },
      remove(name: string, options: { path?: string; domain?: string }) {
        this.set(name, '', { ...options, maxAge: -1 })
      }
    },
    auth: {
      flowType: 'pkce',
      detectSessionInUrl: true,
      persistSession: true,
      autoRefreshToken: true,
    }
  }
)

export function CaseStudiesClient({
  department,
  allCaseStudies,
  allLabels,
}: CaseStudiesClientProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedLabels, setSelectedLabels] = useState<string[]>([])
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState<'likes' | 'retweets' | 'impressions' | 'newest'>('newest')
  const [isLoading, setIsLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedCaseStudy, setSelectedCaseStudy] = useState<CaseStudy | null>(null)
  const [userRole, setUserRole] = useState<UserRole>('guest')
  const [filteredStudies, setFilteredStudies] = useState(allCaseStudies)
  const [allCaseStudiesState, setAllCaseStudies] = useState(allCaseStudies)

  useEffect(() => {
    const initialCaseStudies = allCaseStudies.map(study => ({
      ...study,
      date_of_post: new Date(study.date_of_post),
      id: BigInt(study.id),
      impressions: BigInt(study.impressions),
      likes: BigInt(study.likes),
      retweets: BigInt(study.retweets)
    }))
    setAllCaseStudies(initialCaseStudies)
    setFilteredStudies(initialCaseStudies)
  }, [allCaseStudies])

  useEffect(() => {
    async function getUserRole() {
      const { data: { session } } = await supabase.auth.getSession()
      if (session?.user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', session.user.id)
          .single()
        
        if (profile?.role) {
          setUserRole(profile.role as UserRole)
        }
      } else {
        setUserRole('guest')
      }
      setIsLoading(false)
    }

    getUserRole()
  }, [])

  // Filter studies based on search term and labels
  useEffect(() => {
    const filteredStudies = allCaseStudiesState
      .filter((caseStudy) => {
        if (searchTerm === "") return true;
        return caseStudy.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          caseStudy.twitter_account.toLowerCase().includes(searchTerm.toLowerCase())
      })
      .filter((caseStudy) => {
        if (selectedLabels.length === 0) return true;
        return selectedLabels.every((label) => caseStudy.categories.includes(label))
      })
      .sort((a, b) => {
        switch (sortBy) {
          case 'impressions':
            return Number(b.impressions) - Number(a.impressions)
          case 'newest':
          default:
            return b.date_of_post.getTime() - a.date_of_post.getTime()
        }
      })

    setFilteredStudies(filteredStudies)
  }, [searchTerm, selectedLabels, sortBy, allCaseStudiesState])

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success('Copied to clipboard!')
    } catch {
      toast.error('Failed to copy to clipboard')
    }
  }

  const handleFilterChange = (labels: string[]) => {
    setSelectedLabels(labels)
  }

  const handleCaseStudyClick = (caseStudy: CaseStudy) => {
    setSelectedCaseStudy(caseStudy)
    setIsDialogOpen(true)
  }

  const handleAddNew = () => {
    setSelectedCaseStudy(null)
    setIsDialogOpen(true)
  }

  const renderCaseStudyCard = (caseStudy: CaseStudy) => {
    const handle = caseStudy.twitter_account.split('/').pop() || ''
    const displayName = caseStudy.twitter_account
    const tweetId = caseStudy.twitter_link.split('/status/')?.[1]?.split('?')?.[0]
    
    return (
      <div className="bg-white rounded-lg border shadow-sm hover:shadow-md transition-all">
        <div className="p-3">
          {/* Author Info */}
          <div className="flex items-center gap-2 mb-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={`https://unavatar.io/twitter/${handle}`} alt={displayName} />
              <AvatarFallback>{displayName[0]}</AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium text-sm">{displayName}</p>
              <p className="text-xs text-muted-foreground">@{handle}</p>
            </div>
          </div>

          {/* Description */}
          <p className="text-sm mb-2">{caseStudy.description}</p>

          {/* Tweet Embed */}
          <div className="mb-2">
            {tweetId && <TweetWrapper id={tweetId} />}
          </div>

          {/* Categories */}
          <div className="flex flex-wrap gap-1 mb-2">
            {caseStudy.categories.map((category) => (
              <Badge
                key={category}
                variant="secondary"
                className="px-2 py-0 text-xs hover:bg-secondary/80 cursor-pointer"
                onClick={() => handleFilterChange([category])}
              >
                {category}
              </Badge>
            ))}
          </div>

          {/* Stats */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                {Number(caseStudy.impressions).toLocaleString()}
              </div>
              <div className="flex items-center gap-1">
                <Heart className="h-3 w-3" />
                {Number(caseStudy.likes).toLocaleString()}
              </div>
              <div className="flex items-center gap-1">
                <Repeat2 className="h-3 w-3" />
                {Number(caseStudy.retweets).toLocaleString()}
              </div>
            </div>
            {department.case_studies_admin_level === userRole && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2"
                onClick={() => handleCaseStudyClick(caseStudy)}
              >
                <Pencil className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderCaseStudyList = (caseStudy: CaseStudy) => {
    const handle = caseStudy.twitter_account.split('/').pop() || ''
    const displayName = caseStudy.twitter_account
    
    return (
      <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div>
            <p className="font-medium">{displayName}</p>
            <p className="text-sm text-gray-500">@{handle}</p>
          </div>
        </div>
        <p className="text-gray-700 mb-4">{caseStudy.description}</p>
        <div className="flex flex-wrap gap-2 mb-4">
          {caseStudy.categories.map((category) => (
            <Badge
              key={category}
              variant="secondary"
              className="cursor-pointer"
              onClick={() => handleFilterChange([category])}
            >
              {category}
            </Badge>
          ))}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex gap-4">
            <div className="flex flex-col gap-1">
              <p className="text-sm font-medium">Impressions</p>
              <p className="text-2xl font-bold">{Number(caseStudy.impressions).toLocaleString()}</p>
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-sm font-medium">Likes</p>
              <p className="text-2xl font-bold">{Number(caseStudy.likes).toLocaleString()}</p>
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-sm font-medium">Retweets</p>
              <p className="text-2xl font-bold">{Number(caseStudy.retweets).toLocaleString()}</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(caseStudy.twitter_link)}
            >
              <Link2 className="h-4 w-4 mr-1" />
              Copy Link
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(caseStudy.twitter_link, '_blank')}
            >
              <Twitter className="h-4 w-4 mr-1" />
              View
            </Button>
            {department.case_studies_admin_level === userRole && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleCaseStudyClick(caseStudy)}
              >
                <Pencil className="h-4 w-4 mr-1" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderAdminControls = () => {
    return (
      <div className="flex items-center gap-2">
        <Button onClick={handleAddNew} variant="outline">
          Add New Case Study
        </Button>
      </div>
    )
  }

  const renderCaseStudyDialog = () => {
    if (!isDialogOpen) return null

    return (
      <CaseStudyDialog
        caseStudy={selectedCaseStudy}
        isOpen={isDialogOpen}
        onClose={() => {
          setIsDialogOpen(false)
          setSelectedCaseStudy(null)
        }}
        onCaseStudyChange={(updatedCaseStudy: CaseStudy | null) => {
          if (!updatedCaseStudy) {
            // Handle deletion
            setFilteredStudies(prev => prev.filter(cs => cs.id !== selectedCaseStudy?.id))
            setAllCaseStudies(prev => prev.filter(cs => cs.id !== selectedCaseStudy?.id))
            return
          }
          // Handle update
          const updatedCaseStudies = allCaseStudiesState.map(cs => 
            cs.id === updatedCaseStudy.id ? updatedCaseStudy : cs
          )
          setFilteredStudies(updatedCaseStudies)
          setAllCaseStudies(updatedCaseStudies)
        }}
      />
    )
  }

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>
  }

  return (
    <div className="flex flex-col">
      {/* Header Controls */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="flex items-center justify-between p-4 gap-2">
          <div className="flex items-center gap-2 flex-1">
            <Input
              type="text"
              placeholder="Search case studies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon" className="h-9 w-9">
                  <Filter className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {allLabels.map((label) => (
                  <DropdownMenuCheckboxItem
                    key={label}
                    checked={selectedLabels.includes(label)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedLabels([...selectedLabels, label])
                      } else {
                        setSelectedLabels(selectedLabels.filter((l) => l !== label))
                      }
                    }}
                  >
                    {label}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="flex items-center gap-2">
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as typeof sortBy)}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Sort by..." />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Sort by</SelectLabel>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="likes">Most Likes</SelectItem>
                  <SelectItem value="retweets">Most Retweets</SelectItem>
                  <SelectItem value="impressions">Most Impressions</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
            <div className="flex border rounded-lg overflow-hidden">
              <Button
                variant={viewType === 'grid' ? 'default' : 'ghost'}
                size="icon"
                className="h-9 w-9"
                onClick={() => setViewType('grid')}
              >
                <LayoutGrid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewType === 'list' ? 'default' : 'ghost'}
                size="icon"
                className="h-9 w-9"
                onClick={() => setViewType('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4">
        {viewType === 'grid' ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredStudies.map((caseStudy) => (
              <div key={String(caseStudy.id)} className="h-full">
                {renderCaseStudyCard(caseStudy)}
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-4 max-w-5xl mx-auto">
            {filteredStudies.map((caseStudy) => (
              <div key={String(caseStudy.id)}>
                {renderCaseStudyList(caseStudy)}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Case Study Dialog */}
      {renderCaseStudyDialog()}
      {department.case_studies_admin_level === userRole && renderAdminControls()}
    </div>
  )
}