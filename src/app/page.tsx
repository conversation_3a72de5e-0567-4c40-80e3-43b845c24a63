'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useEffect, useState } from "react"
import axios, { AxiosError } from "axios"
import Link from "next/link"
import { NavBar } from "@/components/ui/tubelight-navbar"
import { LayoutDashboard, FileText, Globe, Code2, User, BarChart2, Megaphone, Newspaper } from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { motion } from "framer-motion"

interface CreatorStats {
  interactions: number
  influencer_rank: string
  posts_active: number
  followers: number
  creator_name: string
  creator_display_name: string
  creator_avatar: string
  creator_rank: number
  interactions_24h: number
  topic_influence: Array<{
    topic: string
    count: number
    percent: number
    rank: number
  }>
}

export default function LandingPage() {
  const [creatorStats, setCreatorStats] = useState<CreatorStats>({
    interactions: 0,
    influencer_rank: 'N/A',
    posts_active: 0,
    followers: 0,
    creator_name: '',
    creator_display_name: '',
    creator_avatar: '',
    creator_rank: 0,
    interactions_24h: 0,
    topic_influence: []
  })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const navItems = [
    {
      name: "Overview",
      url: "#overview",
      icon: LayoutDashboard
    },
    {
      name: "Stats",
      url: "#stats",
      icon: BarChart2
    },
    {
      name: "Case Studies",
      url: "#case-studies",
      icon: FileText
    }
  ]

  useEffect(() => {
    const fetchCreatorStats = async () => {
      try {
        const API_KEY = process.env.NEXT_PUBLIC_LUNARCRUSH_API_KEY;
        if (!API_KEY) {
          throw new Error('LunarCrush API key is missing');
        }
    
        const response = await axios({
          url: 'https://lunarcrush.com/api4/public/creator/twitter/MarioNawfal/v1',
          headers: {
            'Authorization': `Bearer ${API_KEY}`,
            'Accept': 'application/json'
          }
        });
        
        const data = response.data;
        if (data) {
          setCreatorStats({
            interactions: data.interactions || 0,
            influencer_rank: data.influencer_rank || 'N/A',
            posts_active: data.posts_active || 0,
            followers: data.creator_followers || 0,
            creator_name: data.creator_name || '',
            creator_display_name: data.creator_display_name || '',
            creator_avatar: data.creator_avatar || '',
            creator_rank: data.creator_rank || 0,
            interactions_24h: data.interactions_24h || 0,
            topic_influence: data.topic_influence || []
          })
        }
        setIsLoading(false)
      } catch (error) {
        console.error('Error fetching creator stats:', error);
        if (error instanceof AxiosError && error.response?.status === 402) {
          setError('Payment required - Please upgrade your API plan');
        } else if (!process.env.NEXT_PUBLIC_LUNARCRUSH_API_KEY) {
          setError('API Key is missing');
        } else {
          setError('Failed to fetch creator stats');
        }
        setIsLoading(false)
      }
    };

    fetchCreatorStats();
  }, []);

  return (
    <div className="min-h-screen bg-background flex flex-col items-center relative py-16 md:py-24">
      <NavBar items={navItems} />

      {/* Hero Section */}
      <section id="overview" className="container mx-auto px-4 flex flex-col items-center justify-center text-center py-12 md:py-16">
        <div className="max-w-[980px] flex flex-col items-center gap-6">
          <h1 className="text-4xl font-extrabold leading-tight tracking-tighter md:text-5xl lg:text-6xl">
            Case Studies & Press Coverage
          </h1>
          <p className="max-w-[700px] text-lg text-muted-foreground">
            Discover how leading brands and organizations achieve remarkable growth through innovative strategies and data-driven campaigns.
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section id="stats" className="container mx-auto px-4 py-12 md:py-16">
        <div className="flex items-center gap-2 mb-8 justify-center">
          <h2 className="text-4xl font-bold">Our Stats</h2>
        </div>
        
        <Tabs defaultValue="mario" className="w-full max-w-6xl mx-auto">
          <TabsList className="grid w-full grid-cols-3 mb-8">
            <TabsTrigger value="mario" className="text-lg flex items-center gap-2">
              <User className="h-5 w-5" />
              Mario Nawfal
            </TabsTrigger>
            <TabsTrigger value="roundtable" className="text-lg flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Roundtable
            </TabsTrigger>
            <TabsTrigger value="townhall" className="text-lg flex items-center gap-2">
              <Code2 className="h-5 w-5" />
              Crypto TownHall
            </TabsTrigger>
          </TabsList>

          <TabsContent value="mario">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Interactions</h3>
                <div className="text-2xl font-bold">
                  {isLoading ? 'Loading...' : error || creatorStats.interactions.toLocaleString()}
                </div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Influencer Rank</h3>
                <div className="text-2xl font-bold">
                  {isLoading ? 'Loading...' : error || creatorStats.influencer_rank}
                </div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Active Posts</h3>
                <div className="text-2xl font-bold">
                  {isLoading ? 'Loading...' : error || creatorStats.posts_active.toLocaleString()}
                </div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Followers</h3>
                <div className="text-2xl font-bold">
                  {isLoading ? 'Loading...' : error || creatorStats.followers.toLocaleString()}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="roundtable">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Daily Viewers</h3>
                <div className="text-2xl font-bold">25K+</div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Episodes</h3>
                <div className="text-2xl font-bold">350+</div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Guest Speakers</h3>
                <div className="text-2xl font-bold">500+</div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Community Size</h3>
                <div className="text-2xl font-bold">100K+</div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="townhall">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Weekly Viewers</h3>
                <div className="text-2xl font-bold">15K+</div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Sessions</h3>
                <div className="text-2xl font-bold">200+</div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Expert Speakers</h3>
                <div className="text-2xl font-bold">300+</div>
              </div>
              <div className="p-6 rounded-lg border bg-white hover:shadow-md transition-all">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Community Size</h3>
                <div className="text-2xl font-bold">50K+</div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </section>

      {/* Case Studies Section */}
      <section id="case-studies" className="container mx-auto px-4 py-12 md:py-16 pb-16 pt-16">
        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
          {/* Media Case Studies */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="group relative rounded-lg border p-6 hover:shadow-md transition-all bg-white"
          >
            <motion.div 
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100"
            >
              <Megaphone className="h-6 w-6 text-blue-600" />
            </motion.div>
            <h3 className="mt-4 text-lg font-medium">Media Case Studies</h3>
            <p className="mt-2 text-muted-foreground">
              Track and analyze social media engagement, reach, and conversion metrics across platforms.
            </p>
            <Link href="/case-studies/social-media" className="mt-4 inline-block w-full">
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button className="w-full" variant="outline">View Studies</Button>
              </motion.div>
            </Link>
          </motion.div>

          {/* Marketing Case Studies */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="group relative rounded-lg border p-6 hover:shadow-md transition-all bg-white"
          >
            <motion.div 
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100"
            >
              <BarChart2 className="h-6 w-6 text-green-600" />
            </motion.div>
            <h3 className="mt-4 text-lg font-medium">Marketing Case Studies</h3>
            <p className="mt-2 text-muted-foreground">
              Explore successful marketing campaigns and their measurable results across industries.
            </p>
            <Link href="/case-studies/marketing" className="mt-4 inline-block w-full">
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button className="w-full" variant="outline">View Studies</Button>
              </motion.div>
            </Link>
          </motion.div>

          {/* Press Releases */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="group relative rounded-lg border p-6 hover:shadow-md transition-all bg-white"
          >
            <motion.div 
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100"
            >
              <Newspaper className="h-6 w-6 text-purple-600" />
            </motion.div>
            <h3 className="mt-4 text-lg font-medium">Press Releases</h3>
            <p className="mt-2 text-muted-foreground">
              Access our latest press releases, news coverage, and media announcements.
            </p>
            <Link href="/coming-soon" className="mt-4 inline-block w-full">
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button className="w-full" variant="outline">View Releases</Button>
              </motion.div>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  )
}