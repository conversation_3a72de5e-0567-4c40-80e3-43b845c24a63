'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { TrendingUp, Target, BarChart3, LineChart, PieChart, Share2, Twitter, BarChart2, Home, FileText, Globe, Star, ChevronLeft, ChevronRight } from "lucide-react"
import { NavBar } from "@/components/ui/tubelight-navbar"
import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect } from "react"

const reviews = [
  {
    id: 1,
    text: "The marketing strategies implemented have transformed our digital presence. The team's innovative approach and data-driven decisions led to exceptional results.",
    author: "<PERSON>",
    role: "CMO at TechVision",
    rating: 5,
    image: "https://randomuser.me/api/portraits/women/1.jpg"
  },
  {
    id: 2,
    text: "Outstanding work on our brand positioning and social media campaigns. The engagement metrics and ROI exceeded our expectations significantly.",
    author: "<PERSON>",
    role: "Marketing Director at GlobalBrands",
    rating: 5,
    image: "https://randomuser.me/api/portraits/men/2.jpg"
  },
  {
    id: 3,
    text: "The team's ability to understand our vision and translate it into effective marketing campaigns was remarkable. They delivered beyond our expectations.",
    author: "<PERSON>",
    role: "Brand Manager at InnovateTech",
    rating: 5,
    image: "https://randomuser.me/api/portraits/women/3.jpg"
  }
]

export default function MarketingCaseStudies() {
  const [currentReview, setCurrentReview] = useState(0)
  const [direction, setDirection] = useState(0)

  // Auto-advance carousel
  useEffect(() => {
    const timer = setInterval(() => {
      setDirection(1)
      setCurrentReview((prev) => (prev + 1) % reviews.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [])

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    })
  }

  const swipeConfidenceThreshold = 10000
  const swipePower = (offset: number, velocity: number) => {
    return Math.abs(offset) * velocity
  }

  const paginate = (newDirection: number) => {
    setDirection(newDirection)
    setCurrentReview((prev) => (prev + newDirection + reviews.length) % reviews.length)
  }

  const navItems = [
    {
      name: "Home",
      url: "/",
      icon: Home
    },
    {
      name: "Media",
      url: "/case-studies/social-media",
      icon: Globe
    },
    {
      name: "Marketing",
      url: "/case-studies/marketing",
      icon: FileText
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <NavBar items={navItems} />

      {/* Hero Section */}
      <section className="container mx-auto px-4 pt-24 pb-6">
        <div className="max-w-[980px] mx-auto">
          <h1 className="text-4xl font-extrabold leading-tight tracking-tighter md:text-5xl lg:text-6xl text-center mb-4">
            Marketing Case Studies
          </h1>
          <p className="text-lg text-muted-foreground text-center max-w-[700px] mx-auto mb-8">
            Discover how leading brands achieved remarkable growth through innovative marketing strategies and data-driven campaigns.
          </p>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="container mx-auto px-4 mb-12">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Review Screenshot 1 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="group relative aspect-[4/3] rounded-lg overflow-hidden bg-muted"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-black/0 transition-opacity group-hover:opacity-100 opacity-0" />
              <img
                src="https://placehold.co/800x600/f5f5f5/a3a3a3?text=Review+Screenshot+1"
                alt="Marketing Review 1"
                className="object-cover w-full h-full transition-transform group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white translate-y-full group-hover:translate-y-0 transition-transform">
                <p className="font-medium">Campaign Performance Review</p>
                <p className="text-sm opacity-80">Social Media Impact Analysis</p>
              </div>
            </motion.div>

            {/* Review Screenshot 2 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="group relative aspect-[4/3] rounded-lg overflow-hidden bg-muted"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-black/0 transition-opacity group-hover:opacity-100 opacity-0" />
              <img
                src="https://placehold.co/800x600/f5f5f5/a3a3a3?text=Review+Screenshot+2"
                alt="Marketing Review 2"
                className="object-cover w-full h-full transition-transform group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white translate-y-full group-hover:translate-y-0 transition-transform">
                <p className="font-medium">Engagement Metrics</p>
                <p className="text-sm opacity-80">ROI and Conversion Analysis</p>
              </div>
            </motion.div>

            {/* Review Screenshot 3 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="group relative aspect-[4/3] rounded-lg overflow-hidden bg-muted"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-black/0 transition-opacity group-hover:opacity-100 opacity-0" />
              <img
                src="https://placehold.co/800x600/f5f5f5/a3a3a3?text=Review+Screenshot+3"
                alt="Marketing Review 3"
                className="object-cover w-full h-full transition-transform group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white translate-y-full group-hover:translate-y-0 transition-transform">
                <p className="font-medium">Brand Growth Results</p>
                <p className="text-sm opacity-80">Market Penetration Study</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Client Cards Grid */}
      <section className="container mx-auto px-4 py-8">
        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 max-w-6xl mx-auto">
          {/* Client Card 1 */}
          <div className="group relative rounded-lg border p-8 hover:shadow-md transition-all bg-white">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold">Nike</h3>
                <p className="text-sm text-muted-foreground">Sports & Lifestyle</p>
              </div>
            </div>
            <div className="space-y-2 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Campaign ROI</span>
                <span className="font-medium">350%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Engagement Rate</span>
                <span className="font-medium">12.8%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Conversion Rate</span>
                <span className="font-medium">4.2%</span>
              </div>
            </div>
            <Link href="/case-studies/marketing/nike">
              <Button className="w-full" variant="outline">View Profile</Button>
            </Link>
          </div>

          {/* Client Card 2 */}
          <div className="group relative rounded-lg border p-8 hover:shadow-md transition-all bg-white">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <BarChart3 className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold">Spotify</h3>
                <p className="text-sm text-muted-foreground">Music Streaming</p>
              </div>
            </div>
            <div className="space-y-2 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">User Growth</span>
                <span className="font-medium">245%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Retention Rate</span>
                <span className="font-medium">82%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Market Share</span>
                <span className="font-medium">31%</span>
              </div>
            </div>
            <Button className="w-full" variant="outline" disabled>Coming Soon</Button>
          </div>

          {/* Client Card 3 */}
          <div className="group relative rounded-lg border p-8 hover:shadow-md transition-all bg-white">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                <LineChart className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold">Airbnb</h3>
                <p className="text-sm text-muted-foreground">Travel & Hospitality</p>
              </div>
            </div>
            <div className="space-y-2 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Booking Growth</span>
                <span className="font-medium">178%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Customer LTV</span>
                <span className="font-medium">$4,200</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Brand Awareness</span>
                <span className="font-medium">92%</span>
              </div>
            </div>
            <Button className="w-full" variant="outline" disabled>Coming Soon</Button>
          </div>

          {/* Client Card 4 */}
          <div className="group relative rounded-lg border p-8 hover:shadow-md transition-all bg-white">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
                <Share2 className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <h3 className="font-semibold">Tesla</h3>
                <p className="text-sm text-muted-foreground">Automotive & Tech</p>
              </div>
            </div>
            <div className="space-y-2 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Social Reach</span>
                <span className="font-medium">15M+</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Media Coverage</span>
                <span className="font-medium">450+</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Lead Generation</span>
                <span className="font-medium">225%</span>
              </div>
            </div>
            <Button className="w-full" variant="outline" disabled>Coming Soon</Button>
          </div>

          {/* Client Card 5 */}
          <div className="group relative rounded-lg border p-8 hover:shadow-md transition-all bg-white">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <TrendingUp className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <h3 className="font-semibold">Adobe</h3>
                <p className="text-sm text-muted-foreground">Software & Design</p>
              </div>
            </div>
            <div className="space-y-2 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Revenue Growth</span>
                <span className="font-medium">156%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">User Adoption</span>
                <span className="font-medium">89%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Market Position</span>
                <span className="font-medium">#1</span>
              </div>
            </div>
            <Button className="w-full" variant="outline" disabled>Coming Soon</Button>
          </div>

          {/* Client Card 6 */}
          <div className="group relative rounded-lg border p-8 hover:shadow-md transition-all bg-white">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-cyan-100">
                <PieChart className="h-6 w-6 text-cyan-600" />
              </div>
              <div>
                <h3 className="font-semibold">Shopify</h3>
                <p className="text-sm text-muted-foreground">E-commerce</p>
              </div>
            </div>
            <div className="space-y-2 mb-6">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Merchant Growth</span>
                <span className="font-medium">312%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Platform GMV</span>
                <span className="font-medium">$42B</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Partner Network</span>
                <span className="font-medium">24K+</span>
              </div>
            </div>
            <Button className="w-full" variant="outline" disabled>Coming Soon</Button>
          </div>
        </div>
      </section>
    </div>
  )
} 