'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { NavBar } from "@/components/ui/tubelight-navbar"
import { LayoutDashboard, Globe, Code2, Twitter, Users, BarChart2, MessagesSquare, Send, FileText, FileImage, Link2, Download, Home, Star, Quote } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

// Mock data for charts
const xData = [
  { name: 'Jan', impressions: 2.4, followers: 1.8, engagement: 3.2 },
  { name: 'Feb', impressions: 3.1, followers: 2.0, engagement: 3.8 },
  { name: 'Mar', impressions: 2.9, followers: 2.3, engagement: 3.5 },
  { name: 'Apr', impressions: 3.5, followers: 2.6, engagement: 4.0 },
  { name: 'May', impressions: 3.8, followers: 2.9, engagement: 4.3 },
  { name: 'Jun', impressions: 4.2, followers: 3.2, engagement: 4.8 },
]

const discordData = [
  { name: 'Mon', visitors: 1200, newMembers: 180, retention: 75 },
  { name: 'Tue', visitors: 1400, newMembers: 220, retention: 78 },
  { name: 'Wed', visitors: 1300, newMembers: 200, retention: 72 },
  { name: 'Thu', visitors: 1500, newMembers: 250, retention: 80 },
  { name: 'Fri', visitors: 1600, newMembers: 280, retention: 82 },
  { name: 'Sat', visitors: 1800, newMembers: 320, retention: 85 },
  { name: 'Sun', visitors: 1700, newMembers: 300, retention: 83 },
]

const telegramData = [
  { name: 'Week 1', newMembers: 450 },
  { name: 'Week 2', newMembers: 520 },
  { name: 'Week 3', newMembers: 580 },
  { name: 'Week 4', newMembers: 620 },
]

export default function NikeProfile() {
  const navItems = [
    {
      name: "Home",
      url: "/",
      icon: Home
    },
    {
      name: "Media",
      url: "/case-studies/social-media",
      icon: Globe
    },
    {
      name: "Marketing",
      url: "/case-studies/marketing",
      icon: FileText
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <NavBar items={navItems} />

      {/* Profile Header */}
      <section className="container mx-auto px-4 pt-24 pb-12">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center gap-6 mb-8">
            <div className="h-24 w-24 rounded-full bg-blue-100 flex items-center justify-center">
              <img 
                src="/nike-logo.png" 
                alt="Nike Logo" 
                className="h-16 w-16 object-contain"
                onError={(e) => {
                  e.currentTarget.src = "https://via.placeholder.com/150?text=Nike"
                }}
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold mb-2">Nike</h1>
              <p className="text-muted-foreground max-w-2xl">
                Global leader in athletic footwear, apparel, and sports equipment. 
                Known for innovative products and marketing campaigns that inspire athletes worldwide.
              </p>
            </div>
          </div>

          {/* Relevant Files Section */}
          <div className="mt-8 border-t pt-8">
            <h2 className="text-xl font-semibold mb-4">Relevant Files</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Marketing Strategy PDF */}
              <div className="flex items-center gap-4 p-4 rounded-lg border bg-white hover:shadow-md transition-all group cursor-pointer">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-red-100">
                  <FileText className="h-5 w-5 text-red-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium truncate">Marketing Strategy 2024</h3>
                  <p className="text-xs text-muted-foreground">PDF • 2.4 MB</p>
                </div>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100">
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              {/* Brand Guidelines */}
              <div className="flex items-center gap-4 p-4 rounded-lg border bg-white hover:shadow-md transition-all group cursor-pointer">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                  <FileImage className="h-5 w-5 text-blue-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium truncate">Brand Guidelines</h3>
                  <p className="text-xs text-muted-foreground">PDF • 5.1 MB</p>
                </div>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100">
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              {/* Campaign Results */}
              <div className="flex items-center gap-4 p-4 rounded-lg border bg-white hover:shadow-md transition-all group cursor-pointer">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                  <BarChart2 className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium truncate">Q4 2023 Campaign Results</h3>
                  <p className="text-xs text-muted-foreground">PDF • 1.8 MB</p>
                </div>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100">
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              {/* Case Study Link */}
              <div className="flex items-center gap-4 p-4 rounded-lg border bg-white hover:shadow-md transition-all group cursor-pointer">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
                  <Link2 className="h-5 w-5 text-purple-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium truncate">Social Media Strategy</h3>
                  <p className="text-xs text-muted-foreground">Detailed Case Study</p>
                </div>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100">
                  <Link2 className="h-4 w-4" />
                </Button>
              </div>

              {/* Research Report */}
              <div className="flex items-center gap-4 p-4 rounded-lg border bg-white hover:shadow-md transition-all group cursor-pointer">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-orange-100">
                  <FileText className="h-5 w-5 text-orange-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium truncate">Market Research Report</h3>
                  <p className="text-xs text-muted-foreground">PDF • 3.2 MB</p>
                </div>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100">
                  <Download className="h-4 w-4" />
                </Button>
              </div>

              {/* Presentation */}
              <div className="flex items-center gap-4 p-4 rounded-lg border bg-white hover:shadow-md transition-all group cursor-pointer">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-100">
                  <FileText className="h-5 w-5 text-yellow-600" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium truncate">Campaign Presentation</h3>
                  <p className="text-xs text-muted-foreground">PPT • 8.5 MB</p>
                </div>
                <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Media Metrics */}
      <section className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto grid gap-8">
          {/* X Metrics */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Twitter className="h-5 w-5" />
                <CardTitle>X (Twitter) Analytics</CardTitle>
              </div>
              <CardDescription>Monthly performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={xData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="impressions" stroke="#2563eb" name="Impressions (M)" />
                    <Line type="monotone" dataKey="followers" stroke="#16a34a" name="Followers (M)" />
                    <Line type="monotone" dataKey="engagement" stroke="#dc2626" name="Engagement (%)" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Discord Metrics */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <MessagesSquare className="h-5 w-5" />
                <CardTitle>Discord Analytics</CardTitle>
              </div>
              <CardDescription>Weekly community metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={discordData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="visitors" stroke="#2563eb" name="Weekly Visitors" />
                    <Line type="monotone" dataKey="newMembers" stroke="#16a34a" name="New Members" />
                    <Line type="monotone" dataKey="retention" stroke="#dc2626" name="Retention (%)" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Telegram Metrics */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                <CardTitle>Telegram Analytics</CardTitle>
              </div>
              <CardDescription>Monthly member growth</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={telegramData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="newMembers" stroke="#2563eb" name="New Members" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="container mx-auto px-4 py-12 border-t">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Campaign Reviews</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Performance metrics and campaign results from our marketing initiatives.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Review Screenshot 1 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="group relative aspect-[4/3] rounded-lg overflow-hidden bg-muted"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-black/0 transition-opacity group-hover:opacity-100 opacity-0" />
              <img
                src="https://placehold.co/800x600/f5f5f5/a3a3a3?text=Social+Media+Performance"
                alt="Social Media Performance"
                className="object-cover w-full h-full transition-transform group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white translate-y-full group-hover:translate-y-0 transition-transform">
                <p className="font-medium">Social Media Performance</p>
                <p className="text-sm opacity-80">Q4 2023 Campaign Results</p>
              </div>
            </motion.div>

            {/* Review Screenshot 2 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="group relative aspect-[4/3] rounded-lg overflow-hidden bg-muted"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-black/0 transition-opacity group-hover:opacity-100 opacity-0" />
              <img
                src="https://placehold.co/800x600/f5f5f5/a3a3a3?text=Engagement+Analytics"
                alt="Engagement Analytics"
                className="object-cover w-full h-full transition-transform group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white translate-y-full group-hover:translate-y-0 transition-transform">
                <p className="font-medium">Engagement Analytics</p>
                <p className="text-sm opacity-80">Platform Performance Metrics</p>
              </div>
            </motion.div>

            {/* Review Screenshot 3 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="group relative aspect-[4/3] rounded-lg overflow-hidden bg-muted"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-black/0 transition-opacity group-hover:opacity-100 opacity-0" />
              <img
                src="https://placehold.co/800x600/f5f5f5/a3a3a3?text=Growth+Metrics"
                alt="Growth Metrics"
                className="object-cover w-full h-full transition-transform group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white translate-y-full group-hover:translate-y-0 transition-transform">
                <p className="font-medium">Growth Metrics</p>
                <p className="text-sm opacity-80">ROI and Conversion Data</p>
              </div>
            </motion.div>
          </div>

          {/* Overall Performance Summary */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-12 text-center"
          >
            <div className="inline-flex items-center gap-4 bg-white px-6 py-3 rounded-full border">
              <div className="text-lg font-semibold">Campaign Performance</div>
              <div className="text-sm text-muted-foreground">Q4 2023 Results</div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
} 