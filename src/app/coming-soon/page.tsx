'use client'

import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { NavBar } from "@/components/ui/tubelight-navbar"
import { Home, Globe, FileText } from "lucide-react"
import { motion } from "framer-motion"

export default function ComingSoon() {
  const navItems = [
    {
      name: "Home",
      url: "/",
      icon: Home
    },
    {
      name: "Media",
      url: "/case-studies/social-media",
      icon: Globe
    },
    {
      name: "Marketing",
      url: "/case-studies/marketing",
      icon: FileText
    }
  ]

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background p-4">
      <NavBar items={navItems} />
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-6"
      >
        <motion.h1 
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl"
        >
          Coming Soon
        </motion.h1>
        <motion.p 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="text-lg text-muted-foreground max-w-[600px] mx-auto"
        >
          We're working hard to bring you something amazing. Stay tuned!
        </motion.p>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Button asChild>
            <Link href="/">
              Return Home
            </Link>
          </Button>
        </motion.div>
      </motion.div>
    </div>
  )
} 