import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // List of paths that should not be redirected
  const allowedPaths = ['/coming-soon', '/', '/case-studies/social-media', '/case-studies/marketing', '/case-studies/marketing/nike']
  
  // Don't redirect if the path is in the allowed list
  if (allowedPaths.includes(request.nextUrl.pathname)) {
    return NextResponse.next()
  }

  // Redirect all other routes to coming-soon
  return NextResponse.redirect(new URL('/coming-soon', request.url))
}

export const config = {
  matcher: '/((?!api|_next/static|_next/image|favicon.ico).*)',
} 