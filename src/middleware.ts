import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // List of paths that should not be redirected
  const allowedPaths = [
    '/coming-soon',
    '/',
    '/case-studies/social-media',
    '/case-studies/marketing',
    '/case-studies/marketing/nike'
  ]

  // Don't redirect if the path is in the allowed list
  if (allowedPaths.includes(pathname)) {
    // Log successful access in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🌐 [MIDDLEWARE] Allowed access to: ${pathname}`)
    }
    return NextResponse.next()
  }

  // Log redirect in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 [MIDDLEWARE] Redirecting ${pathname} to /coming-soon`)
  }

  // Redirect all other routes to coming-soon
  return NextResponse.redirect(new URL('/coming-soon', request.url))
}

export const config = {
  matcher: '/((?!api|_next/static|_next/image|favicon.ico).*)',
} 