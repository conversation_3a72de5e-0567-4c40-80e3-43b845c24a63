/**
 * Comprehensive logging utility for the application
 * Provides structured logging with different levels and contexts
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error'
type LogContext = 'api' | 'ui' | 'auth' | 'database' | 'external' | 'performance' | 'user'

interface LogEntry {
  level: LogLevel
  context: LogContext
  message: string
  timestamp: string
  data?: any
  error?: Error
  userId?: string
  sessionId?: string
}

class Logger {
  private isDevelopment: boolean
  private sessionId: string

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development'
    this.sessionId = this.generateSessionId()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private formatTimestamp(): string {
    return new Date().toISOString()
  }

  private createLogEntry(
    level: LogLevel,
    context: LogContext,
    message: string,
    data?: any,
    error?: Error,
    userId?: string
  ): LogEntry {
    return {
      level,
      context,
      message,
      timestamp: this.formatTimestamp(),
      data,
      error,
      userId,
      sessionId: this.sessionId,
    }
  }

  private shouldLog(level: LogLevel): boolean {
    if (this.isDevelopment) return true
    
    // In production, only log warnings and errors
    return level === 'warn' || level === 'error'
  }

  private formatMessage(entry: LogEntry): string {
    const emoji = {
      debug: '🔍',
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌',
    }

    const contextEmoji = {
      api: '🌐',
      ui: '🎨',
      auth: '🔐',
      database: '🗄️',
      external: '🔗',
      performance: '⚡',
      user: '👤',
    }

    return `${emoji[entry.level]} ${contextEmoji[entry.context]} [${entry.context.toUpperCase()}] ${entry.message}`
  }

  private logToConsole(entry: LogEntry): void {
    if (!this.shouldLog(entry.level)) return

    const formattedMessage = this.formatMessage(entry)
    
    switch (entry.level) {
      case 'debug':
        console.debug(formattedMessage, entry.data)
        break
      case 'info':
        console.info(formattedMessage, entry.data)
        break
      case 'warn':
        console.warn(formattedMessage, entry.data)
        break
      case 'error':
        console.error(formattedMessage, entry.data, entry.error)
        break
    }
  }

  // Public logging methods
  debug(context: LogContext, message: string, data?: any, userId?: string): void {
    const entry = this.createLogEntry('debug', context, message, data, undefined, userId)
    this.logToConsole(entry)
  }

  info(context: LogContext, message: string, data?: any, userId?: string): void {
    const entry = this.createLogEntry('info', context, message, data, undefined, userId)
    this.logToConsole(entry)
  }

  warn(context: LogContext, message: string, data?: any, userId?: string): void {
    const entry = this.createLogEntry('warn', context, message, data, undefined, userId)
    this.logToConsole(entry)
  }

  error(context: LogContext, message: string, error?: Error, data?: any, userId?: string): void {
    const entry = this.createLogEntry('error', context, message, data, error, userId)
    this.logToConsole(entry)
  }

  // Specialized logging methods
  apiRequest(method: string, url: string, data?: any): void {
    this.info('api', `${method} ${url}`, { requestData: data })
  }

  apiResponse(method: string, url: string, status: number, data?: any): void {
    const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'info'
    this[level]('api', `${method} ${url} - ${status}`, { responseData: data })
  }

  apiError(method: string, url: string, error: Error, data?: any): void {
    this.error('api', `${method} ${url} failed`, error, { requestData: data })
  }

  userAction(action: string, userId?: string, data?: any): void {
    this.info('user', `User action: ${action}`, data, userId)
  }

  performance(operation: string, duration: number, data?: any): void {
    const level = duration > 1000 ? 'warn' : 'info'
    this[level]('performance', `${operation} took ${duration}ms`, data)
  }

  databaseQuery(query: string, duration?: number, data?: any): void {
    this.debug('database', `Query: ${query}`, { duration, ...data })
  }

  databaseError(query: string, error: Error, data?: any): void {
    this.error('database', `Query failed: ${query}`, error, data)
  }

  authEvent(event: string, userId?: string, data?: any): void {
    this.info('auth', `Auth event: ${event}`, data, userId)
  }

  externalService(service: string, operation: string, data?: any): void {
    this.info('external', `${service}: ${operation}`, data)
  }

  externalServiceError(service: string, operation: string, error: Error, data?: any): void {
    this.error('external', `${service} ${operation} failed`, error, data)
  }

  // Performance timing utilities
  startTimer(label: string): () => void {
    const start = performance.now()
    return () => {
      const duration = performance.now() - start
      this.performance(label, duration)
    }
  }

  // Error boundary logging
  errorBoundary(error: Error, errorInfo: any, componentStack?: string): void {
    this.error('ui', 'React Error Boundary caught an error', error, {
      errorInfo,
      componentStack,
    })
  }
}

// Create and export singleton logger instance
export const logger = new Logger()

// Export types for use in other files
export type { LogLevel, LogContext, LogEntry }
