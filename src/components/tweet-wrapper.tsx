'use client'

import { Tweet } from 'react-tweet'

interface TweetWrapperProps {
  id: string
  onHeightChange?: (height: number) => void
}

export function TweetWrapper({ id }: TweetWrapperProps) {
  if (!id) {
    console.warn('No tweet ID provided to TweetWrapper');
    return null;
  }

  return (
    <div className="w-full flex justify-center">
      <div className="w-full max-w-[550px]">
        <Tweet id={id} />
      </div>
    </div>
  )
}
