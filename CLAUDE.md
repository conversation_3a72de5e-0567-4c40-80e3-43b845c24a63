# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Quick Commands

```bash
# Development
bun dev                              # Start development server (preferred)
npm run dev                          # Alternative using npm

# Building & Production
bun run build                        # Build for production
bun run start                        # Start production server
bun run lint                         # Run ESLint

# Dependencies
bun install                          # Install dependencies (preferred)
bunx shadcn@latest add [component]   # Add new Shadcn components
```

## 🏗️ Architecture Overview

This is a **Next.js 15 case studies application** showcasing marketing campaigns and social media performance data.

### Core Architecture
- **Framework**: Next.js 15 with App Router
- **Runtime**: React 19 with TypeScript
- **Database**: Supabase (PostgreSQL) with SSR integration
- **UI**: Shadcn UI components built on Radix UI primitives
- **Styling**: Tailwind CSS with CSS variables
- **Package Manager**: Bun (preferred) or npm

### Key Data Flow
1. **Server Components** (`src/app/case-studies/page.tsx`) fetch data from Supabase
2. **Client Components** (`src/app/client.tsx`) handle interactive UI
3. **Supabase Integration** uses both client-side and server-side clients:
   - `src/lib/supabase-client.ts` - Client-side operations
   - `src/lib/supabase-server.ts` - Server-side with cookie handling

### Database Schema
The application centers around a `case_studies` table with:
- Twitter metrics (impressions, likes, retweets as BigInt)
- Categories array for filtering
- Date-based sorting and display

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── case-studies/      # Main case studies pages
│   │   ├── marketing/nike/ # Specific campaign pages
│   │   └── social-media/   # Social media case studies
│   ├── client.tsx          # Main client component
│   └── layout.tsx          # Root layout (uses 'use client')
├── components/
│   ├── ui/                 # Shadcn UI components
│   ├── case-study-dialog.tsx
│   └── tweet-wrapper.tsx   # Twitter integration
└── lib/
    ├── supabase-client.ts  # Client-side Supabase
    ├── supabase-server.ts  # Server-side Supabase
    └── utils.ts            # Utility functions
```

## 🔧 Development Patterns

### Supabase Integration
- Use `createClient()` from `supabase-server.ts` in Server Components
- Use `supabase` from `supabase-client.ts` in Client Components
- BigInt handling is required for large numbers (impressions, likes, etc.)

### Component Architecture
- Server Components for data fetching (e.g., `getCaseStudies()`)
- Client Components for interactivity (marked with `'use client'`)
- Shadcn components configured with "new-york" style and neutral base color

### Data Transformation
- Transform Supabase data to match TypeScript types
- Convert strings to BigInt for metrics
- Handle date parsing for `date_of_post` fields

## 🎨 UI System

- **Components**: Shadcn UI with Radix primitives
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Charts**: Recharts for data visualization
- **Toast**: Sonner for notifications
- **Typography**: Inter font family

## 🔐 Environment Variables

Required environment variables:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 📝 Development Notes

- The root layout uses `'use client'` directive
- OpenAI integration should use "gpt-4o-mini" model when needed
- Twitter integration via `react-tweet` component
- Error handling includes fallbacks for missing Supabase data
- Application supports both Bun and npm package managers